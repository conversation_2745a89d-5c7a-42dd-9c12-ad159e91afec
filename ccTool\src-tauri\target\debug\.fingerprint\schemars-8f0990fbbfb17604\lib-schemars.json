{"rustc": 8713626761367032038, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 5144872609095556103, "profile": 13232757476167777671, "path": 15015202836555676576, "deps": [[1662858574012850257, "url", false, 14545318225351099458], [6492276495737738681, "dyn_clone", false, 9488084886467709632], [7048373690260176624, "schemars_derive", false, 13229831576683553065], [9413012258834587937, "indexmap", false, 12552734600741791031], [10633404241517405153, "serde", false, 13710987727815827011], [13928279528217427837, "uuid1", false, 6885246450056038907], [15224074216694330788, "build_script_build", false, 13426681156956826441], [16853464370074921650, "serde_json", false, 94586857394329905]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-8f0990fbbfb17604\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "metadata": 11679674351051169359, "config": 2202906307356721367, "compile_kind": 0}