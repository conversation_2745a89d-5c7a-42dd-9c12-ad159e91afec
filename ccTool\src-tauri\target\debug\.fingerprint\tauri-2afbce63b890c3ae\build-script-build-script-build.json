{"rustc": 8713626761367032038, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 9652763411108993936, "profile": 13232757476167777671, "path": 14238232914004698338, "deps": [[9077808164528454422, "glob", false, 1601299019247452785], [14290461508996001963, "tauri_utils", false, 12174425459524369009], [16526178207807856520, "tauri_build", false, 13037780117790334721], [17175234422038868540, "heck", false, 15885593491917704645]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-2afbce63b890c3ae\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "metadata": 8230821343879830340, "config": 2202906307356721367, "compile_kind": 0}