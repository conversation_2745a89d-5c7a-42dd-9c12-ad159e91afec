{"rustc": 8713626761367032038, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 1741205612033448911, "profile": 13232757476167777671, "path": 6457734146437171241, "deps": [[265113383264221167, "url<PERSON><PERSON>n", false, 3466797956999237756], [496936660758387454, "walkdir", false, 11822186505758346111], [1195130682115775693, "json_patch", false, 13734367552093514958], [1662858574012850257, "url", false, 14545318225351099458], [2486677718701085347, "phf", false, 16500545116106641827], [2964536209444415731, "memchr", false, 7045542772437017294], [4069479833055117039, "semver", false, 16755131620408445464], [5817713708394680873, "infer", false, 8790319695037193915], [6378930360846669252, "serde_with", false, 14502776600347212976], [6403862083583890547, "html5ever", false, 12406670816031020815], [7023022645111830431, "brotli", false, 14369236141078815311], [7764419099803471093, "anyhow", false, 3365035921628201874], [8678281613339888692, "dunce", false, 11296523746312857775], [9077808164528454422, "glob", false, 1601299019247452785], [9273627815139534674, "thiserror", false, 14305360895321711654], [10201151829869029096, "toml", false, 14588877664371097713], [10633404241517405153, "serde", false, 13710987727815827011], [12368485582061518913, "http", false, 6084887467950843812], [13265110353977718284, "ctor", false, 3436394068311821447], [13484275127474016001, "proc_macro2", false, 14687213313952739329], [13928279528217427837, "uuid", false, 6885246450056038907], [15224074216694330788, "schemars", false, 6953754709454579903], [15399619262696441677, "log", false, 9918173250025143322], [16101470096942513125, "kuchiki", false, 17238777549312586835], [16366677067367223141, "regex", false, 13275383048245157121], [16853464370074921650, "serde_json", false, 94586857394329905], [16953957437939878591, "serde_untagged", false, 14380931424429834024], [17525013869477438691, "quote", false, 17980049754427416293], [17783627555355638954, "cargo_metadata", false, 6178600876747812032]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-ad03b651f0b5aa7a\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "metadata": 1965016819931902697, "config": 2202906307356721367, "compile_kind": 0}