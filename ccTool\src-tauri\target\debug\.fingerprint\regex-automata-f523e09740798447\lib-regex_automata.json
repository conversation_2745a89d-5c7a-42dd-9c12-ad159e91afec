{"rustc": 8713626761367032038, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 15630646695703972922, "profile": 12206360443249279867, "path": 782313133386842064, "deps": [[2005677738724344306, "regex_syntax", false, 11137502843070102035], [2964536209444415731, "memchr", false, 7045542772437017294], [7325384046744447800, "aho_corasick", false, 17810759330720445166]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-automata-f523e09740798447\\dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "metadata": 1628557892126071772, "config": 2202906307356721367, "compile_kind": 0}