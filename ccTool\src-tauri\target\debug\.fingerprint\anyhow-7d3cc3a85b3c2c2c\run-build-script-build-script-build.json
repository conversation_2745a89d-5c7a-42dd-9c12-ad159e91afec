{"rustc": 8713626761367032038, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[7764419099803471093, "build_script_build", false, 2598546178232933648]], "local": [{"RerunIfChanged": {"output": "debug\\build\\anyhow-7d3cc3a85b3c2c2c\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}