{"rustc": 8713626761367032038, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 1181824239777920051, "profile": 2386962340253827157, "path": 10046366564651918806, "deps": [[3439881079477119364, "serde_derive", false, 10179221712063594062], [7610906965409985862, "serde_with_macros", false, 10852007844032859694], [10633404241517405153, "serde", false, 12316704220407016875]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-23c9cbc07c378405\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "metadata": 10761897740382732713, "config": 2202906307356721367, "compile_kind": 0}