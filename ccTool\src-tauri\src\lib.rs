use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use walkdir::WalkDir;

#[derive(Debug, Serialize, Deserialize)]
pub struct CacheItem {
    pub id: String,
    pub name: String,
    pub description: String,
    pub path: Option<String>,
    pub size: Option<u64>,
    pub item_type: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CleanResult {
    pub success: bool,
    pub message: String,
    pub cleaned_items: Vec<String>,
    pub errors: Vec<String>,
}

fn get_user_dir() -> Option<PathBuf> {
    dirs::home_dir()
}

fn calculate_dir_size(path: &PathBuf) -> u64 {
    let mut total_size = 0;
    if let Ok(entries) = WalkDir::new(path).into_iter().collect::<Result<Vec<_>, _>>() {
        for entry in entries {
            if let Ok(metadata) = entry.metadata() {
                if metadata.is_file() {
                    total_size += metadata.len();
                }
            }
        }
    }
    total_size
}

// 获取浏览器缓存路径
fn get_browser_cache_paths() -> Vec<CacheItem> {
    let mut cache_items = Vec::new();

    if let Some(user_dir) = get_user_dir() {
        let cache_paths = vec![
            // Chrome
            (
                "chrome_cache",
                "Chrome 缓存",
                "Google Chrome 浏览器缓存文件",
                user_dir.join("AppData/Local/Google/Chrome/User Data/Default/Cache"),
            ),
            // Edge
            (
                "edge_cache",
                "Edge 缓存",
                "Microsoft Edge 浏览器缓存文件",
                user_dir.join("AppData/Local/Microsoft/Edge/User Data/Default/Cache"),
            ),
            // Firefox
            (
                "firefox_cache",
                "Firefox 缓存",
                "Mozilla Firefox 浏览器缓存文件",
                user_dir.join("AppData/Local/Mozilla/Firefox/Profiles"),
            ),
        ];

        for (id, name, desc, path) in cache_paths {
            let size = if path.exists() {
                Some(calculate_dir_size(&path))
            } else {
                None
            };

            cache_items.push(CacheItem {
                id: id.to_string(),
                name: name.to_string(),
                description: desc.to_string(),
                path: Some(path.to_string_lossy().to_string()),
                size,
                item_type: "browser_cache".to_string(),
            });
        }
    }

    cache_items
}

// 获取应用缓存路径
fn get_app_cache_paths() -> Vec<CacheItem> {
    let mut cache_items = Vec::new();

    if let Some(user_dir) = get_user_dir() {
        let app_paths = vec![
            (
                "highpower_cache",
                "HighPower 应用缓存",
                "HighPower 应用的缓存数据",
                user_dir.join("AppData/Roaming/highpower"),
            ),
            (
                "mattverse_cache",
                "MattVerse 应用缓存",
                "MattVerse 应用的缓存数据",
                user_dir.join("AppData/Roaming/mattverse"),
            ),
            (
                "mattview_cache",
                "MattView 应用缓存",
                "MattView 应用的缓存数据",
                user_dir.join("AppData/Roaming/mattview"),
            ),
        ];

        for (id, name, desc, path) in app_paths {
            let size = if path.exists() {
                Some(calculate_dir_size(&path))
            } else {
                None
            };

            cache_items.push(CacheItem {
                id: id.to_string(),
                name: name.to_string(),
                description: desc.to_string(),
                path: Some(path.to_string_lossy().to_string()),
                size,
                item_type: "app_cache".to_string(),
            });
        }
    }

    cache_items
}

// 获取Web存储路径
fn get_web_storage_paths() -> Vec<CacheItem> {
    let mut cache_items = Vec::new();

    if let Some(user_dir) = get_user_dir() {
        let storage_paths = vec![
            (
                "chrome_localstorage",
                "Chrome LocalStorage",
                "Chrome 本地存储数据",
                user_dir.join("AppData/Local/Google/Chrome/User Data/Default/Local Storage"),
            ),
            (
                "chrome_sessionstorage",
                "Chrome SessionStorage",
                "Chrome 会话存储数据",
                user_dir.join("AppData/Local/Google/Chrome/User Data/Default/Session Storage"),
            ),
            (
                "chrome_indexeddb",
                "Chrome IndexedDB",
                "Chrome IndexedDB 数据库",
                user_dir.join("AppData/Local/Google/Chrome/User Data/Default/IndexedDB"),
            ),
            (
                "chrome_cookies",
                "Chrome Cookies",
                "Chrome Cookie 文件",
                user_dir.join("AppData/Local/Google/Chrome/User Data/Default/Cookies"),
            ),
        ];

        for (id, name, desc, path) in storage_paths {
            let size = if path.exists() {
                Some(calculate_dir_size(&path))
            } else {
                None
            };

            cache_items.push(CacheItem {
                id: id.to_string(),
                name: name.to_string(),
                description: desc.to_string(),
                path: Some(path.to_string_lossy().to_string()),
                size,
                item_type: "web_storage".to_string(),
            });
        }
    }

    cache_items
}

// 安全删除目录或文件
fn safe_remove_path(path: &PathBuf) -> Result<(), std::io::Error> {
    if path.is_dir() {
        fs::remove_dir_all(path)
    } else if path.is_file() {
        fs::remove_file(path)
    } else {
        Ok(()) // 路径不存在，认为删除成功
    }
}

#[tauri::command]
pub async fn fetch_cache_items() -> Result<Vec<CacheItem>, String> {
    let mut all_items = Vec::new();
    all_items.extend(get_browser_cache_paths());
    all_items.extend(get_app_cache_paths());
    all_items.extend(get_web_storage_paths());
    Ok(all_items)
}

#[tauri::command]
pub async fn clean_selected_items(item_ids: Vec<String>) -> Result<CleanResult, String> {
    let mut result = CleanResult {
        success: true,
        message: String::new(),
        cleaned_items: Vec::new(),
        errors: Vec::new(),
    };

    let all_items = fetch_cache_items().await.map_err(|e| e.to_string())?;

    for item_id in item_ids {
        if let Some(item) = all_items.iter().find(|i| i.id == item_id) {
            if let Some(path_str) = &item.path {
                let path = PathBuf::from(path_str);
                match safe_remove_path(&path) {
                    Ok(_) => result.cleaned_items.push(item.name.clone()),
                    Err(e) => {
                        result.errors.push(format!("清理 {} 失败: {}", item.name, e));
                        result.success = false;
                    }
                }
            }
        } else {
            result.errors.push(format!("未找到缓存项: {}", item_id));
            result.success = false;
        }
    }

    result.message = if result.cleaned_items.is_empty() && result.errors.is_empty() {
        "没有找到需要清理的项目".to_string()
    } else if result.success {
        format!("成功清理了 {} 个项目", result.cleaned_items.len())
    } else {
        format!("清理完成，成功 {} 个，失败 {} 个", result.cleaned_items.len(), result.errors.len())
    };

    Ok(result)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![fetch_cache_items, clean_selected_items])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
