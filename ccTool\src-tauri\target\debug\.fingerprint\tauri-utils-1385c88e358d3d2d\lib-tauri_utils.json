{"rustc": 8713626761367032038, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 1741205612033448911, "profile": 12206360443249279867, "path": 6457734146437171241, "deps": [[265113383264221167, "url<PERSON><PERSON>n", false, 3698538677671196370], [496936660758387454, "walkdir", false, 9213723630201655546], [1195130682115775693, "json_patch", false, 9803625595086911247], [1662858574012850257, "url", false, 17134354906006443267], [2486677718701085347, "phf", false, 16654262739703721310], [2964536209444415731, "memchr", false, 7045542772437017294], [4069479833055117039, "semver", false, 11878560852104875333], [5817713708394680873, "infer", false, 14279287413867557544], [6378930360846669252, "serde_with", false, 9349998487757267352], [7023022645111830431, "brotli", false, 14369236141078815311], [7764419099803471093, "anyhow", false, 3365035921628201874], [8678281613339888692, "dunce", false, 11296523746312857775], [9077808164528454422, "glob", false, 1601299019247452785], [9273627815139534674, "thiserror", false, 14305360895321711654], [10201151829869029096, "toml", false, 3941408820346108040], [10633404241517405153, "serde", false, 12316704220407016875], [12368485582061518913, "http", false, 6084887467950843812], [13265110353977718284, "ctor", false, 3436394068311821447], [13928279528217427837, "uuid", false, 4704800055605471744], [15399619262696441677, "log", false, 9918173250025143322], [16366677067367223141, "regex", false, 13275383048245157121], [16853464370074921650, "serde_json", false, 18036703308269674016], [16953957437939878591, "serde_untagged", false, 11464034649092098773]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-1385c88e358d3d2d\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "metadata": 1965016819931902697, "config": 2202906307356721367, "compile_kind": 0}