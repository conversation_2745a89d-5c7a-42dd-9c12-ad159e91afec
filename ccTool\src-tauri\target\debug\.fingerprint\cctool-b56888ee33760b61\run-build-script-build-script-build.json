{"rustc": 8713626761367032038, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10513200597999880729, "build_script_build", false, 13307992959827840014], [12189284016743244684, "build_script_build", false, 18364422131139888434], [4167181399501426191, "build_script_build", false, 5802569542718934343]], "local": [{"RerunIfChanged": {"output": "debug\\build\\cctool-b56888ee33760b61\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}