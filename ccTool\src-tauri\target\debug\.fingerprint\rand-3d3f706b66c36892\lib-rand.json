{"rustc": 8713626761367032038, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"rand_pcg\", \"small_rng\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 721237385257707553, "profile": 13232757476167777671, "path": 9656865627408123174, "deps": [[3045318065146905707, "rand_pcg", false, 10604802592064529986], [6453573393678185459, "getrandom_package", false, 3381270367166805041], [11761531122794857361, "rand_core", false, 3867102748789722638], [15629295216311830669, "rand_chacha", false, 4188219467096229379]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-3d3f706b66c36892\\dep-lib-rand", "checksum": false}}], "rustflags": [], "metadata": 7119169968661360791, "config": 2202906307356721367, "compile_kind": 0}