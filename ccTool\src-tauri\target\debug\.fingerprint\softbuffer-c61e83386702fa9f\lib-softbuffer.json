{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 11597174340557729424, "profile": 12206360443249279867, "path": 3734363173919044871, "deps": [[6410343819635645113, "raw_window_handle", false, 1141997125662469209], [10080649455407339182, "windows_sys", false, 12013339502633674884], [15399619262696441677, "log", false, 9918173250025143322], [16731215249320019310, "build_script_build", false, 349726078188101416]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-c61e83386702fa9f\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "metadata": 2524053766866278946, "config": 2202906307356721367, "compile_kind": 0}