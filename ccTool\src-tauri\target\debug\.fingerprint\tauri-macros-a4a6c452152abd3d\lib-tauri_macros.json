{"rustc": 8713626761367032038, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 7890680933942864499, "profile": 13232757476167777671, "path": 12217772313669072040, "deps": [[676514103441133901, "tauri_codegen", false, 11316667117738290250], [13484275127474016001, "proc_macro2", false, 14687213313952739329], [14290461508996001963, "tauri_utils", false, 12174425459524369009], [15679091240300861453, "syn", false, 13689480832507077811], [17175234422038868540, "heck", false, 15885593491917704645], [17525013869477438691, "quote", false, 17980049754427416293]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-a4a6c452152abd3d\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "metadata": 2550541321619233985, "config": 2202906307356721367, "compile_kind": 0}