{"rustc": 8713626761367032038, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 10676753825826352750, "profile": 13232757476167777671, "path": 11585440129236827747, "deps": [[2989785442755699193, "crc32fast", false, 6810778015470405751], [12522475594372247311, "miniz_oxide", false, 18336632459498126752]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-65446a27e651edb1\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "metadata": 1284714256429684901, "config": 2202906307356721367, "compile_kind": 0}