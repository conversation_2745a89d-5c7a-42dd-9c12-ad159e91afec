{"rustc": 8713626761367032038, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12189284016743244684, "build_script_build", false, 18364422131139888434], [4167181399501426191, "build_script_build", false, 11093398609925450431]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-opener-f383f84e9f4f8721\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}