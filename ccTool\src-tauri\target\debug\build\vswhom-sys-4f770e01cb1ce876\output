cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=ext/vswhom.cpp
OPT_LEVEL = Some(0)
OUT_DIR = Some(C:\Users\<USER>\Desktop\clearTool\ccTool\src-tauri\target\debug\build\vswhom-sys-4f770e01cb1ce876\out)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\Desktop\clearTool\ccTool\src-tauri\target\debug\deps;C:\Users\<USER>\Desktop\clearTool\ccTool\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Users\<USER>\Desktop\clearTool\ccTool\node_modules\.bin;C:\Users\<USER>\AppData\Local\node\corepack\v1\pnpm\10.13.1\dist\node-gyp-bin;C:\Program Files\PowerShell\7;c:\myFile\applications\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\myFile\applications\Bandizip\;C:\myFile\applications\nvm;C:\Program Files\nodejs;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-11\bin;C:\myFile\programData\mysql-8.0.26-winx64\bin;C:\myFile\programData\MongoDB\Server\8.0\bin;C:\TDengine;C:\Program Files\PowerShell\7\;C:\Program Files\CursorModifier;C:\myFile\programData\Go\bin;C:\myFile\programData\Go\bin;C:\myFile\workspaces\BackEnd\GoProject\bin;C:\Program Files\Docker\Docker\resources\bin;C:\myFile\programData\Apache-2.4.57\Apache24\bin;C:\ProgramData\ComposerSetup\bin;C:\myFile\programData\PHProgram\php8\ext;C:\myFile\programData\PHProgram\php8;C:\myFile\programData\PHProgram\php5\ext;C:\m;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\myFile\downloads\coder\cunzhi-cli-v0.3.8-windows-x86_64;C:\Users\<USER>\AppData\Local\pnpm;C:\myFile\programData\Python\Python3\Scripts\;C:\myFile\programData\Python\Python3\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\gitkraken\bin;C:\myFile\applications\nvm;C:\Program Files\nodejs;C:\myFile\applications\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\myFile\applications\Kiro\bin;C:\myFile\applications\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CXX_x86_64-pc-windows-msvc
CXX_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXX_x86_64_pc_windows_msvc
CXX_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXX
HOST_CXX = None
cargo:rerun-if-env-changed=CXX
CXX = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_pc_windows_msvc
CXXFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-pc-windows-msvc
CXXFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
vswhom.cpp
ext/vswhom.cpp(213): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
ext/vswhom.cpp(216): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
ext/vswhom.cpp(431): warning C4456: declaration of 'hr' hides previous local declaration
ext/vswhom.cpp(418): note: see declaration of 'hr'
ext/vswhom.cpp(459): warning C4244: 'argument': conversion from 'LONGLONG' to 'int', possible loss of data
ext/vswhom.cpp(502): warning C4456: declaration of 'rc' hides previous local declaration
ext/vswhom.cpp(410): note: see declaration of 'rc'
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=vswhom
cargo:rustc-link-search=native=C:\Users\<USER>\Desktop\clearTool\ccTool\src-tauri\target\debug\build\vswhom-sys-4f770e01cb1ce876\out
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64-pc-windows-msvc
CXXSTDLIB_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64_pc_windows_msvc
CXXSTDLIB_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXXSTDLIB
HOST_CXXSTDLIB = None
cargo:rerun-if-env-changed=CXXSTDLIB
CXXSTDLIB = None
cargo:rustc-link-lib=dylib=OleAut32
cargo:rustc-link-lib=dylib=Ole32
