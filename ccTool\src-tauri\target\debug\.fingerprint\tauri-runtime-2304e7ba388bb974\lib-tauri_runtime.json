{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 3865210990427632890, "profile": 12206360443249279867, "path": 7026492476197513654, "deps": [[1662858574012850257, "url", false, 17134354906006443267], [3269169667440160398, "cookie", false, 17785535093195881924], [5390965438110126853, "dpi", false, 15678683625505155603], [6410343819635645113, "raw_window_handle", false, 1141997125662469209], [8513271038360652471, "build_script_build", false, 3807744700089223677], [9273627815139534674, "thiserror", false, 14305360895321711654], [10633404241517405153, "serde", false, 12316704220407016875], [12263755691840680691, "webview2_com", false, 2837580089365972505], [12368485582061518913, "http", false, 6084887467950843812], [12393227384234373415, "windows", false, 9652073730210531348], [14290461508996001963, "tauri_utils", false, 3881338086521838925], [16853464370074921650, "serde_json", false, 18036703308269674016]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-2304e7ba388bb974\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "metadata": 12594724888323783112, "config": 2202906307356721367, "compile_kind": 0}