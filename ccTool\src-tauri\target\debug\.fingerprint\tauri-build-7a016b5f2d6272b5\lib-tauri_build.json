{"rustc": 8713626761367032038, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 16041980555811317082, "profile": 13232757476167777671, "path": 5131864505358578593, "deps": [[496936660758387454, "walkdir", false, 11822186505758346111], [1195130682115775693, "json_patch", false, 13734367552093514958], [4069479833055117039, "semver", false, 16755131620408445464], [7764419099803471093, "anyhow", false, 3365035921628201874], [8247200863094774500, "cargo_toml", false, 14470335105593808705], [9077808164528454422, "glob", false, 1601299019247452785], [10201151829869029096, "toml", false, 14588877664371097713], [10633404241517405153, "serde", false, 13710987727815827011], [12232489923304414993, "tauri_winres", false, 9574933642337078564], [13899702929860959449, "dirs", false, 8296498552086580168], [14290461508996001963, "tauri_utils", false, 12174425459524369009], [15224074216694330788, "schemars", false, 6953754709454579903], [16853464370074921650, "serde_json", false, 94586857394329905], [17175234422038868540, "heck", false, 15885593491917704645]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-7a016b5f2d6272b5\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "metadata": 14747373215743437585, "config": 2202906307356721367, "compile_kind": 0}