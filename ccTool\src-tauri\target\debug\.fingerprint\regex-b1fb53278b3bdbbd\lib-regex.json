{"rustc": 8713626761367032038, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 12206360443249279867, "path": 9770723813183388447, "deps": [[2005677738724344306, "regex_syntax", false, 11137502843070102035], [2964536209444415731, "memchr", false, 7045542772437017294], [7325384046744447800, "aho_corasick", false, 17810759330720445166], [16886577961293800131, "regex_automata", false, 12304451107755462464]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-b1fb53278b3bdbbd\\dep-lib-regex", "checksum": false}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}