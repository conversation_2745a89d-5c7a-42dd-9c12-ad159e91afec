import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Trash2, RefreshCw, HardDrive, Database, Globe, CheckSquare, Square } from 'lucide-react';
import { CacheItem, CleanResult, CacheGroup } from '@/types';
import { formatFileSize, getCacheTypeTitle } from '@/lib/format';

const CacheCleaner: React.FC = () => {
  const [cacheItems, setCacheItems] = useState<CacheItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [cleaning, setCleaning] = useState(false);
  const [message, setMessage] = useState<string>('');

  // 获取缓存项
  const fetchCacheItems = async () => {
    setLoading(true);
    try {
      const items: CacheItem[] = await invoke('fetch_cache_items');
      setCacheItems(items);
      setMessage('');
    } catch (error) {
      setMessage(`获取缓存信息失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 清理选中的缓存项
  const cleanSelectedItems = async () => {
    if (selectedItems.size === 0) {
      setMessage('请选择要清理的项目');
      return;
    }

    setCleaning(true);
    try {
      const result: CleanResult = await invoke('clean_selected_items', {
        itemIds: Array.from(selectedItems)
      });
      
      setMessage(result.message);
      if (result.errors.length > 0) {
        setMessage(prev => prev + '\n错误详情:\n' + result.errors.join('\n'));
      }
      
      // 清理成功后刷新列表
      if (result.success) {
        setSelectedItems(new Set());
        await fetchCacheItems();
      }
    } catch (error) {
      setMessage(`清理失败: ${error}`);
    } finally {
      setCleaning(false);
    }
  };

  // 切换单个项目选择状态
  const toggleItem = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  // 切换分组选择状态
  const toggleGroup = (groupItems: CacheItem[]) => {
    const groupIds = groupItems.map(item => item.id);
    const newSelected = new Set(selectedItems);
    
    const allSelected = groupIds.every(id => newSelected.has(id));
    
    if (allSelected) {
      // 取消选择所有
      groupIds.forEach(id => newSelected.delete(id));
    } else {
      // 选择所有
      groupIds.forEach(id => newSelected.add(id));
    }
    
    setSelectedItems(newSelected);
  };

  // 全选/取消全选
  const toggleAll = () => {
    if (selectedItems.size === cacheItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(cacheItems.map(item => item.id)));
    }
  };

  // 按类型分组缓存项
  const groupedItems: CacheGroup[] = React.useMemo(() => {
    const groups: Record<string, CacheItem[]> = {};
    
    cacheItems.forEach(item => {
      if (!groups[item.item_type]) {
        groups[item.item_type] = [];
      }
      groups[item.item_type].push(item);
    });

    return Object.entries(groups).map(([type, items]) => ({
      type,
      title: getCacheTypeTitle(type),
      items
    }));
  }, [cacheItems]);

  // 获取图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'browser_cache':
        return <Globe className="h-5 w-5" />;
      case 'web_storage':
        return <Database className="h-5 w-5" />;
      case 'app_cache':
        return <HardDrive className="h-5 w-5" />;
      default:
        return <HardDrive className="h-5 w-5" />;
    }
  };

  // 计算总大小
  const totalSize = cacheItems.reduce((sum, item) => sum + (item.size || 0), 0);
  const selectedSize = cacheItems
    .filter(item => selectedItems.has(item.id))
    .reduce((sum, item) => sum + (item.size || 0), 0);

  useEffect(() => {
    fetchCacheItems();
  }, []);

  return (
    <div className="w-full h-screen bg-gray-50 flex flex-col">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Trash2 className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">系统清理工具</h1>
              <p className="text-sm text-gray-500">清理浏览器缓存、Web存储和应用缓存</p>
            </div>
          </div>
          <button
            onClick={fetchCacheItems}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6 text-sm text-gray-600">
            <span>总计: {cacheItems.length} 项 ({formatFileSize(totalSize)})</span>
            <span>已选: {selectedItems.size} 项 ({formatFileSize(selectedSize)})</span>
          </div>
          <button
            onClick={toggleAll}
            disabled={cacheItems.length === 0}
            className="flex items-center gap-2 px-3 py-1.5 text-sm text-blue-600 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
          >
            {selectedItems.size === cacheItems.length ? (
              <>
                <CheckSquare className="h-4 w-4" />
                取消全选
              </>
            ) : (
              <>
                <Square className="h-4 w-4" />
                全选
              </>
            )}
          </button>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 overflow-auto">
        {loading ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <RefreshCw className="h-8 w-8 animate-spin mb-3" />
            <p>正在扫描缓存...</p>
          </div>
        ) : (
          <div className="p-6 space-y-4">
            {groupedItems.map(group => (
              <div key={group.type} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getTypeIcon(group.type)}
                      <h3 className="font-medium text-gray-900">{group.title}</h3>
                      <span className="text-sm text-gray-500">({group.items.length} 项)</span>
                    </div>
                    <button
                      onClick={() => toggleGroup(group.items)}
                      className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                    >
                      {group.items.every(item => selectedItems.has(item.id)) ? '取消选择' : '选择全部'}
                    </button>
                  </div>
                </div>
                <div className="divide-y divide-gray-100">
                  {group.items.map(item => (
                    <div
                      key={item.id}
                      className="flex items-center gap-3 p-4 hover:bg-gray-50 transition-colors"
                    >
                      <button
                        onClick={() => toggleItem(item.id)}
                        className="flex-shrink-0"
                      >
                        {selectedItems.has(item.id) ? (
                          <CheckSquare className="h-5 w-5 text-blue-600" />
                        ) : (
                          <Square className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-gray-900 text-sm">{item.name}</div>
                        <div className="text-xs text-gray-500 truncate">{item.description}</div>
                      </div>
                      <div className="text-sm text-gray-500 font-mono">
                        {formatFileSize(item.size)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部操作栏 */}
      <div className="bg-white border-t border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {selectedItems.size > 0 ? `已选择 ${selectedItems.size} 项` : '请选择要清理的项目'}
          </div>
          <button
            onClick={cleanSelectedItems}
            disabled={selectedItems.size === 0 || cleaning}
            className="flex items-center gap-2 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {cleaning ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                清理中...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                开始清理
              </>
            )}
          </button>
        </div>
      </div>

      {/* 消息提示 */}
      {message && (
        <div className="absolute bottom-20 left-6 right-6">
          <div className={`p-4 rounded-lg shadow-lg ${
            message.includes('失败') || message.includes('错误')
              ? 'bg-red-50 text-red-700 border border-red-200'
              : 'bg-green-50 text-green-700 border border-green-200'
          }`}>
            <div className="whitespace-pre-line text-sm">{message}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CacheCleaner;
