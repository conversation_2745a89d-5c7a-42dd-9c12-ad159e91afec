import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Trash2, RefreshCw, HardDrive, Database, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { CacheItem, CleanResult, CacheGroup } from '@/types';
import { formatFileSize, getCacheTypeTitle } from '@/lib/format';

const CacheCleaner: React.FC = () => {
  const [cacheItems, setCacheItems] = useState<CacheItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [cleaning, setCleaning] = useState(false);
  const [message, setMessage] = useState<string>('');

  // 获取缓存项
  const fetchCacheItems = async () => {
    setLoading(true);
    try {
      const items: CacheItem[] = await invoke('fetch_cache_items');
      setCacheItems(items);
      setMessage('');
    } catch (error) {
      setMessage(`获取缓存信息失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 清理选中的缓存项
  const cleanSelectedItems = async () => {
    if (selectedItems.size === 0) {
      setMessage('请选择要清理的项目');
      return;
    }

    setCleaning(true);
    try {
      const result: CleanResult = await invoke('clean_selected_items', {
        itemIds: Array.from(selectedItems)
      });
      
      setMessage(result.message);
      if (result.errors.length > 0) {
        setMessage(prev => prev + '\n错误详情:\n' + result.errors.join('\n'));
      }
      
      // 清理成功后刷新列表
      if (result.success) {
        setSelectedItems(new Set());
        await fetchCacheItems();
      }
    } catch (error) {
      setMessage(`清理失败: ${error}`);
    } finally {
      setCleaning(false);
    }
  };

  // 切换单个项目选择状态
  const toggleItem = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  // 切换分组选择状态
  const toggleGroup = (groupItems: CacheItem[]) => {
    const groupIds = groupItems.map(item => item.id);
    const newSelected = new Set(selectedItems);
    
    const allSelected = groupIds.every(id => newSelected.has(id));
    
    if (allSelected) {
      // 取消选择所有
      groupIds.forEach(id => newSelected.delete(id));
    } else {
      // 选择所有
      groupIds.forEach(id => newSelected.add(id));
    }
    
    setSelectedItems(newSelected);
  };

  // 全选/取消全选
  const toggleAll = () => {
    if (selectedItems.size === cacheItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(cacheItems.map(item => item.id)));
    }
  };

  // 按类型分组缓存项
  const groupedItems: CacheGroup[] = React.useMemo(() => {
    const groups: Record<string, CacheItem[]> = {};
    
    cacheItems.forEach(item => {
      if (!groups[item.item_type]) {
        groups[item.item_type] = [];
      }
      groups[item.item_type].push(item);
    });

    return Object.entries(groups).map(([type, items]) => ({
      type,
      title: getCacheTypeTitle(type),
      items
    }));
  }, [cacheItems]);

  // 获取图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'browser_cache':
        return <Globe className="h-5 w-5" />;
      case 'web_storage':
        return <Database className="h-5 w-5" />;
      case 'app_cache':
        return <HardDrive className="h-5 w-5" />;
      default:
        return <HardDrive className="h-5 w-5" />;
    }
  };

  // 计算总大小
  const totalSize = cacheItems.reduce((sum, item) => sum + (item.size || 0), 0);
  const selectedSize = cacheItems
    .filter(item => selectedItems.has(item.id))
    .reduce((sum, item) => sum + (item.size || 0), 0);

  useEffect(() => {
    fetchCacheItems();
  }, []);

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-6 w-6" />
            系统清理工具
          </CardTitle>
          <CardDescription>
            清理浏览器缓存、Web存储和应用缓存，释放磁盘空间
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 统计信息 */}
          <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
            <div className="text-sm">
              <div>总计: {cacheItems.length} 项 ({formatFileSize(totalSize)})</div>
              <div>已选: {selectedItems.size} 项 ({formatFileSize(selectedSize)})</div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchCacheItems}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleAll}
                disabled={cacheItems.length === 0}
              >
                {selectedItems.size === cacheItems.length ? '取消全选' : '全选'}
              </Button>
            </div>
          </div>

          {/* 缓存项列表 */}
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p>正在扫描缓存...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {groupedItems.map(group => (
                <Card key={group.type} className="border-l-4 border-l-primary">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getTypeIcon(group.type)}
                        <CardTitle className="text-lg">{group.title}</CardTitle>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleGroup(group.items)}
                      >
                        {group.items.every(item => selectedItems.has(item.id)) ? '取消选择' : '选择全部'}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {group.items.map(item => (
                      <div
                        key={item.id}
                        className="flex items-center space-x-3 p-2 rounded hover:bg-muted/50"
                      >
                        <Checkbox
                          checked={selectedItems.has(item.id)}
                          onCheckedChange={() => toggleItem(item.id)}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm">{item.name}</div>
                          <div className="text-xs text-muted-foreground truncate">
                            {item.description}
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatFileSize(item.size)}
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* 清理按钮 */}
          <div className="flex gap-2">
            <Button
              onClick={cleanSelectedItems}
              disabled={selectedItems.size === 0 || cleaning}
              className="flex-1"
            >
              {cleaning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  清理中...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  清理选中项 ({selectedItems.size})
                </>
              )}
            </Button>
          </div>

          {/* 消息显示 */}
          {message && (
            <div className={`p-3 rounded-lg text-sm whitespace-pre-line ${
              message.includes('失败') || message.includes('错误') 
                ? 'bg-destructive/10 text-destructive' 
                : 'bg-green-50 text-green-700'
            }`}>
              {message}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CacheCleaner;
