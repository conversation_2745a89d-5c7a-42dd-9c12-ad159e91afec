{"rustc": 8713626761367032038, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1729391596725106306, "profile": 12206360443249279867, "path": 15420526998016768682, "deps": [[1210841155870401058, "tao", false, 264094982972156114], [1662858574012850257, "url", false, 17134354906006443267], [6410343819635645113, "raw_window_handle", false, 1141997125662469209], [7243888681965211836, "build_script_build", false, 8253213091965512609], [8244776183334334055, "once_cell", false, 8290770194285094856], [8513271038360652471, "tauri_runtime", false, 16121744879486123959], [12263755691840680691, "webview2_com", false, 2837580089365972505], [12368485582061518913, "http", false, 6084887467950843812], [12393227384234373415, "windows", false, 9652073730210531348], [13968538145695823328, "wry", false, 11297412581550050763], [14290461508996001963, "tauri_utils", false, 3881338086521838925], [15399619262696441677, "log", false, 9918173250025143322], [16731215249320019310, "softbuffer", false, 15419742451000858386]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-fa16a6e47fa9dbb6\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "metadata": 6527887627390916949, "config": 2202906307356721367, "compile_kind": 0}