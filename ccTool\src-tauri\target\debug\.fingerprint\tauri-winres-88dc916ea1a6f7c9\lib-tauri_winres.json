{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[]", "target": 13892393709442824890, "profile": 13232757476167777671, "path": 12921677837311230696, "deps": [[6378311218707679683, "embed_resource", false, 1902321862578441053], [10201151829869029096, "toml", false, 14588877664371097713]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-88dc916ea1a6f7c9\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "metadata": 12380690817683880479, "config": 2202906307356721367, "compile_kind": 0}