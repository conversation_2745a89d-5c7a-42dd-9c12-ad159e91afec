{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9652763411108993936, "profile": 13232757476167777671, "path": 10161372638217020969, "deps": [[7856826338644234777, "cfg_aliases", false, 1079102625820134744]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-4ad08b707da42ec6\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "metadata": 2524053766866278946, "config": 2202906307356721367, "compile_kind": 0}