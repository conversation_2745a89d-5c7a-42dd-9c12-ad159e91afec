{"rustc": 8713626761367032038, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 8088998362596673410, "profile": 12206360443249279867, "path": 14944448734340268629, "deps": [[265113383264221167, "url<PERSON><PERSON>n", false, 3698538677671196370], [1662858574012850257, "url", false, 17134354906006443267], [2615220396250171925, "getrandom", false, 16760756534197540188], [2733971839385166500, "serialize_to_javascript", false, 8856677046569279933], [3269169667440160398, "cookie", false, 17785535093195881924], [3416510266418967711, "muda", false, 4315114367083683650], [5694600528075578689, "percent_encoding", false, 11089040683981871759], [6410343819635645113, "raw_window_handle", false, 1141997125662469209], [7243888681965211836, "tauri_runtime_wry", false, 9106509743233843152], [7470442545028885647, "mime", false, 51268828785710150], [7764419099803471093, "anyhow", false, 3365035921628201874], [8513271038360652471, "tauri_runtime", false, 16121744879486123959], [8670877025618955261, "window_vibrancy", false, 3992648494618137085], [8678281613339888692, "dunce", false, 11296523746312857775], [9009422061445205129, "tauri_macros", false, 559411358938263300], [9077808164528454422, "glob", false, 1601299019247452785], [9273627815139534674, "thiserror", false, 14305360895321711654], [9343801854649291285, "serde_repr", false, 13167733726149712352], [10633404241517405153, "serde", false, 12316704220407016875], [12104263900330614341, "tokio", false, 5600467287340806897], [12189284016743244684, "build_script_build", false, 18364422131139888434], [12263755691840680691, "webview2_com", false, 2837580089365972505], [12368485582061518913, "http", false, 6084887467950843812], [12393227384234373415, "windows", false, 9652073730210531348], [13899702929860959449, "dirs", false, 13846803567285692901], [14290461508996001963, "tauri_utils", false, 3881338086521838925], [15399619262696441677, "log", false, 9918173250025143322], [16853464370074921650, "serde_json", false, 18036703308269674016], [17175234422038868540, "heck", false, 15885593491917704645]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-6f13dd95eebfbac2\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "metadata": 8230821343879830340, "config": 2202906307356721367, "compile_kind": 0}