{"rustc": 8713626761367032038, "features": "[]", "declared_features": "[]", "target": 17716386681368743308, "profile": 13232757476167777671, "path": 17401098152401525726, "deps": [[4782517107791549006, "serde_derive_internals", false, 10179511856399174838], [13484275127474016001, "proc_macro2", false, 14687213313952739329], [15679091240300861453, "syn", false, 13689480832507077811], [17525013869477438691, "quote", false, 17980049754427416293]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars_derive-1ef4bbadd43463ff\\dep-lib-schemars_derive", "checksum": false}}], "rustflags": [], "metadata": 17573214762644668264, "config": 2202906307356721367, "compile_kind": 0}