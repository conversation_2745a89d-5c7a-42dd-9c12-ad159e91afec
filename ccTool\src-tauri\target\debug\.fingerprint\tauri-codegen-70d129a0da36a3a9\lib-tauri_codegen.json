{"rustc": 8713626761367032038, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 6729452749655645294, "profile": 13232757476167777671, "path": 1285491657390224541, "deps": [[496936660758387454, "walkdir", false, 11822186505758346111], [1195130682115775693, "json_patch", false, 13734367552093514958], [1662858574012850257, "url", false, 14545318225351099458], [4069479833055117039, "semver", false, 16755131620408445464], [5123194605536730508, "sha2", false, 16872999084190036032], [7023022645111830431, "brotli", false, 14369236141078815311], [9089814594820591935, "png", false, 8679180911269805803], [9253677898334269643, "base64", false, 13477070436967234309], [9273627815139534674, "thiserror", false, 14305360895321711654], [10633404241517405153, "serde", false, 13710987727815827011], [13202003597468353088, "ico", false, 1448867028497189928], [13484275127474016001, "proc_macro2", false, 14687213313952739329], [13928279528217427837, "uuid", false, 6885246450056038907], [14290461508996001963, "tauri_utils", false, 12174425459524369009], [15679091240300861453, "syn", false, 13689480832507077811], [16853464370074921650, "serde_json", false, 94586857394329905], [17525013869477438691, "quote", false, 17980049754427416293]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-70d129a0da36a3a9\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "metadata": 13121722971692460637, "config": 2202906307356721367, "compile_kind": 0}