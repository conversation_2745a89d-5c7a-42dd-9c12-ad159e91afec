{"rustc": 8713626761367032038, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 9652763411108993936, "profile": 13232757476167777671, "path": 2930559272984319453, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-e60d91ee129d1f66\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "metadata": 6527887627390916949, "config": 2202906307356721367, "compile_kind": 0}