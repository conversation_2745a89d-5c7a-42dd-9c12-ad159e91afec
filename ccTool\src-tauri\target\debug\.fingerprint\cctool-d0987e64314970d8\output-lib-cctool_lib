{"$message_type":"diagnostic","message":"the name `__cmd__get_cache_items` is defined multiple times","code":{"code":"E0255","explanation":"You can't import a value whose name is the same as another value defined in the\nmodule.\n\nErroneous code example:\n\n```compile_fail,E0255\nuse bar::foo; // error: an item named `foo` is already in scope\n\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {}\n```\n\nYou can use aliases in order to fix this error. Example:\n\n```\nuse bar::foo as bar_foo; // ok!\n\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {}\n```\n\nOr you can reference the item with its parent:\n\n```\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {\n    bar::foo(); // we get the item by referring to its parent\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":6250,"byte_end":6265,"line_start":210,"line_end":210,"column_start":14,"column_end":29,"is_primary":true,"text":[{"text":"pub async fn get_cache_items() -> Result<Vec<CacheItem>, String> {","highlight_start":14,"highlight_end":29}],"label":"`__cmd__get_cache_items` reimported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":6218,"byte_end":6235,"line_start":209,"line_end":209,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":"previous definition of the macro `__cmd__get_cache_items` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6218,"byte_end":6235,"line_start":209,"line_end":209,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[tauri::command]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\tauri-macros-2.4.0\\src/lib.rs","byte_start":1025,"byte_end":1098,"line_start":35,"line_end":35,"column_start":1,"column_end":74,"is_primary":false,"text":[{"text":"pub fn command(attributes: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"`__cmd__get_cache_items` must be defined only once in the macro namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove unnecessary import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":6218,"byte_end":6237,"line_start":209,"line_end":210,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18},{"text":"pub async fn get_cache_items() -> Result<Vec<CacheItem>, String> {","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6218,"byte_end":6235,"line_start":209,"line_end":209,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[tauri::command]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\tauri-macros-2.4.0\\src/lib.rs","byte_start":1025,"byte_end":1098,"line_start":35,"line_end":35,"column_start":1,"column_end":74,"is_primary":false,"text":[{"text":"pub fn command(attributes: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0255]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `__cmd__get_cache_items` is defined multiple times\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:210:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[tauri::command]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious definition of the macro `__cmd__get_cache_items` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn get_cache_items() -> Result<Vec<CacheItem>, String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`__cmd__get_cache_items` reimported here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `__cmd__get_cache_items` must be defined only once in the macro namespace of this module\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the name `__cmd__clean_cache_items` is defined multiple times","code":{"code":"E0255","explanation":"You can't import a value whose name is the same as another value defined in the\nmodule.\n\nErroneous code example:\n\n```compile_fail,E0255\nuse bar::foo; // error: an item named `foo` is already in scope\n\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {}\n```\n\nYou can use aliases in order to fix this error. Example:\n\n```\nuse bar::foo as bar_foo; // ok!\n\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {}\n```\n\nOr you can reference the item with its parent:\n\n```\nfn foo() {}\n\nmod bar {\n     pub fn foo() {}\n}\n\nfn main() {\n    bar::foo(); // we get the item by referring to its parent\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":6675,"byte_end":6692,"line_start":227,"line_end":227,"column_start":14,"column_end":31,"is_primary":true,"text":[{"text":"pub async fn clean_cache_items(item_ids: Vec<String>) -> Result<CleanResult, String> {","highlight_start":14,"highlight_end":31}],"label":"`__cmd__clean_cache_items` reimported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":6643,"byte_end":6660,"line_start":226,"line_end":226,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":"previous definition of the macro `__cmd__clean_cache_items` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6643,"byte_end":6660,"line_start":226,"line_end":226,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[tauri::command]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\tauri-macros-2.4.0\\src/lib.rs","byte_start":1025,"byte_end":1098,"line_start":35,"line_end":35,"column_start":1,"column_end":74,"is_primary":false,"text":[{"text":"pub fn command(attributes: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"`__cmd__clean_cache_items` must be defined only once in the macro namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove unnecessary import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":6643,"byte_end":6662,"line_start":226,"line_end":227,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18},{"text":"pub async fn clean_cache_items(item_ids: Vec<String>) -> Result<CleanResult, String> {","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":{"span":{"file_name":"src\\lib.rs","byte_start":6643,"byte_end":6660,"line_start":226,"line_end":226,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[tauri::command]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\tauri-macros-2.4.0\\src/lib.rs","byte_start":1025,"byte_end":1098,"line_start":35,"line_end":35,"column_start":1,"column_end":74,"is_primary":false,"text":[{"text":"pub fn command(attributes: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0255]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `__cmd__clean_cache_items` is defined multiple times\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:227:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[tauri::command]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious definition of the macro `__cmd__clean_cache_items` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m227\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub async fn clean_cache_items(item_ids: Vec<String>) -> Result<CleanResult, String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`__cmd__clean_cache_items` reimported here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `__cmd__clean_cache_items` must be defined only once in the macro namespace of this module\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 2 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0255`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0255`.\u001b[0m\n"}
